"""
🤖 نظام التداول الآلي - Trading Telegram Bot
===========================================

نظام تداول آلي متكامل يستخدم تقنية الذكاء الاصطناعي من Google Gemini
لتحليل أسواق العملات الرقمية واتخاذ قرارات التداول وتنفيذها تلقائيًا.

المؤلف: Augment Agent
الإصدار: 1.0.0
التاريخ: يناير 2025

الميزات:
- تحليل مستمر للسوق باستخدام Gemini AI
- إنشاء توصيات تداول ذكية
- إدارة المخاطر والامتثال الشرعي
- واجهة مستخدم سهلة ومتقدمة
- أمان عالي وتشفير البيانات
"""

from .trading_db import TradingDatabaseManager
from .risk_manager import RiskManager, IslamicComplianceFilter
from .market_analyzer import MarketAnalyzer
from .recommendation_engine import RecommendationEngine
from .auto_trading_system import AutoTradingSystem

__version__ = "1.0.0"
__author__ = "Augment Agent"

# تصدير جميع الكلاسات الرئيسية
__all__ = [
    'TradingDatabaseManager',
    'RiskManager', 
    'IslamicComplianceFilter',
    'MarketAnalyzer',
    'RecommendationEngine',
    'AutoTradingSystem'
]
