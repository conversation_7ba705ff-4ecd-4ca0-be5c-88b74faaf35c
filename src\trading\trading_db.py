"""
📊 إدارة قاعدة البيانات للتداول الآلي
=====================================

يدير جميع العمليات المتعلقة بقاعدة البيانات لنظام التداول الآلي
بما في ذلك التوصيات، الإشارات، الإعدادات، والتاريخ.

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter

logger = logging.getLogger(__name__)

@dataclass
class TradingRecommendation:
    """توصية تداول"""
    id: str
    user_id: str
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    confidence: float  # 0.0 - 1.0
    entry_price: float
    target_price: float
    stop_loss: float
    risk_level: str  # 'low', 'medium', 'high'
    reasoning: str
    created_at: datetime
    expires_at: datetime
    status: str  # 'active', 'executed', 'expired', 'cancelled'
    ai_analysis: str
    technical_indicators: Dict[str, Any]

@dataclass
class TradingSignal:
    """إشارة تداول"""
    id: str
    symbol: str
    signal_type: str  # 'bullish', 'bearish', 'neutral'
    strength: float  # 0.0 - 1.0
    timeframe: str  # '1h', '4h', '1d', etc.
    indicators: Dict[str, Any]
    created_at: datetime
    source: str  # 'technical', 'ai', 'combined'

@dataclass
class UserTradingSettings:
    """إعدادات التداول للمستخدم"""
    user_id: str
    enabled: bool
    risk_tolerance: str  # 'conservative', 'moderate', 'aggressive'
    max_position_size: float
    preferred_symbols: List[str]
    excluded_symbols: List[str]
    notification_enabled: bool
    auto_execute: bool
    islamic_compliance: bool
    created_at: datetime
    updated_at: datetime

class TradingDatabaseManager:
    """مدير قاعدة البيانات للتداول الآلي"""
    
    def __init__(self, db: firestore.Client):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db: مثيل قاعدة بيانات Firestore
        """
        self.db = db
        
        # أسماء المجموعات
        self.RECOMMENDATIONS_COLLECTION = 'trading_recommendations'
        self.SIGNALS_COLLECTION = 'trading_signals'
        self.USER_SETTINGS_COLLECTION = 'user_trading_settings'
        self.TRADING_HISTORY_COLLECTION = 'trading_history'
        self.MARKET_DATA_CACHE = 'market_data_cache'
        
    async def save_recommendation(self, recommendation: TradingRecommendation) -> bool:
        """
        حفظ توصية تداول
        
        Args:
            recommendation: التوصية المراد حفظها
            
        Returns:
            True إذا تم الحفظ بنجاح
        """
        try:
            # تحويل التوصية إلى قاموس
            data = asdict(recommendation)
            
            # تحويل التواريخ إلى نصوص
            data['created_at'] = recommendation.created_at.isoformat()
            data['expires_at'] = recommendation.expires_at.isoformat()
            
            # حفظ في Firestore
            doc_ref = self.db.collection(self.RECOMMENDATIONS_COLLECTION).document(recommendation.id)
            doc_ref.set(data)
            
            logger.info(f"تم حفظ التوصية {recommendation.id} للمستخدم {recommendation.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ التوصية: {str(e)}")
            return False
    
    async def get_user_recommendations(self, user_id: str, status: str = None, limit: int = 10) -> List[TradingRecommendation]:
        """
        الحصول على توصيات المستخدم
        
        Args:
            user_id: معرف المستخدم
            status: حالة التوصيات (اختياري)
            limit: عدد التوصيات المطلوبة
            
        Returns:
            قائمة التوصيات
        """
        try:
            query = self.db.collection(self.RECOMMENDATIONS_COLLECTION).where(
                filter=FieldFilter('user_id', '==', user_id)
            )
            
            if status:
                query = query.where(filter=FieldFilter('status', '==', status))
            
            query = query.order_by('created_at', direction=firestore.Query.DESCENDING).limit(limit)
            
            docs = query.get()
            recommendations = []
            
            for doc in docs:
                data = doc.to_dict()
                # تحويل التواريخ من نصوص
                data['created_at'] = datetime.fromisoformat(data['created_at'])
                data['expires_at'] = datetime.fromisoformat(data['expires_at'])
                
                recommendations.append(TradingRecommendation(**data))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على توصيات المستخدم: {str(e)}")
            return []
    
    async def save_trading_signal(self, signal: TradingSignal) -> bool:
        """
        حفظ إشارة تداول
        
        Args:
            signal: الإشارة المراد حفظها
            
        Returns:
            True إذا تم الحفظ بنجاح
        """
        try:
            data = asdict(signal)
            data['created_at'] = signal.created_at.isoformat()
            
            doc_ref = self.db.collection(self.SIGNALS_COLLECTION).document(signal.id)
            doc_ref.set(data)
            
            logger.info(f"تم حفظ الإشارة {signal.id} للرمز {signal.symbol}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الإشارة: {str(e)}")
            return False
    
    async def get_recent_signals(self, symbol: str = None, hours: int = 24) -> List[TradingSignal]:
        """
        الحصول على الإشارات الحديثة
        
        Args:
            symbol: رمز العملة (اختياري)
            hours: عدد الساعات الماضية
            
        Returns:
            قائمة الإشارات
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            query = self.db.collection(self.SIGNALS_COLLECTION).where(
                filter=FieldFilter('created_at', '>=', cutoff_time.isoformat())
            )
            
            if symbol:
                query = query.where(filter=FieldFilter('symbol', '==', symbol))
            
            query = query.order_by('created_at', direction=firestore.Query.DESCENDING)
            
            docs = query.get()
            signals = []
            
            for doc in docs:
                data = doc.to_dict()
                data['created_at'] = datetime.fromisoformat(data['created_at'])
                signals.append(TradingSignal(**data))
            
            return signals
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإشارات: {str(e)}")
            return []
    
    async def save_user_settings(self, settings: UserTradingSettings) -> bool:
        """
        حفظ إعدادات التداول للمستخدم
        
        Args:
            settings: الإعدادات المراد حفظها
            
        Returns:
            True إذا تم الحفظ بنجاح
        """
        try:
            data = asdict(settings)
            data['created_at'] = settings.created_at.isoformat()
            data['updated_at'] = settings.updated_at.isoformat()
            
            doc_ref = self.db.collection(self.USER_SETTINGS_COLLECTION).document(settings.user_id)
            doc_ref.set(data)
            
            logger.info(f"تم حفظ إعدادات التداول للمستخدم {settings.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات المستخدم: {str(e)}")
            return False
    
    async def get_user_settings(self, user_id: str) -> Optional[UserTradingSettings]:
        """
        الحصول على إعدادات التداول للمستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            إعدادات المستخدم أو None
        """
        try:
            doc_ref = self.db.collection(self.USER_SETTINGS_COLLECTION).document(user_id)
            doc = doc_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                data['created_at'] = datetime.fromisoformat(data['created_at'])
                data['updated_at'] = datetime.fromisoformat(data['updated_at'])
                return UserTradingSettings(**data)
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إعدادات المستخدم: {str(e)}")
            return None
    
    async def cleanup_expired_recommendations(self) -> int:
        """
        تنظيف التوصيات منتهية الصلاحية
        
        Returns:
            عدد التوصيات المحذوفة
        """
        try:
            now = datetime.now()
            
            # البحث عن التوصيات منتهية الصلاحية
            query = self.db.collection(self.RECOMMENDATIONS_COLLECTION).where(
                filter=FieldFilter('expires_at', '<', now.isoformat())
            ).where(
                filter=FieldFilter('status', '==', 'active')
            )
            
            docs = query.get()
            deleted_count = 0
            
            # تحديث حالة التوصيات إلى منتهية الصلاحية
            for doc in docs:
                doc.reference.update({'status': 'expired'})
                deleted_count += 1
            
            logger.info(f"تم تحديث {deleted_count} توصية منتهية الصلاحية")
            return deleted_count
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التوصيات: {str(e)}")
            return 0
    
    async def get_active_users_count(self) -> int:
        """
        الحصول على عدد المستخدمين النشطين في التداول
        
        Returns:
            عدد المستخدمين النشطين
        """
        try:
            query = self.db.collection(self.USER_SETTINGS_COLLECTION).where(
                filter=FieldFilter('enabled', '==', True)
            )
            
            docs = query.get()
            return len(docs)
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على عدد المستخدمين النشطين: {str(e)}")
            return 0
