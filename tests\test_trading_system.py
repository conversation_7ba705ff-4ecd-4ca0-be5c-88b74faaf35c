#!/usr/bin/env python3
"""
🧪 اختبار نظام التداول الآلي
==========================

اختبار بسيط للتأكد من أن جميع مكونات نظام التداول تعمل بشكل صحيح
بدون الحاجة لتشغيل البوت كاملاً.

المؤلف: Augment Agent
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_trading_system():
    """اختبار نظام التداول الآلي"""

    print("بدء اختبار نظام التداول الآلي...")
    print("=" * 50)
    
    # اختبار 1: استيراد الوحدات
    print("\nاختبار استيراد الوحدات...")
    try:
        from trading.trading_db import TradingDatabaseManager, TradingRecommendation
        from trading.risk_manager import RiskManager, RiskLevel
        from trading.market_analyzer import MarketAnalyzer, MarketSignal
        from trading.recommendation_engine import RecommendationEngine
        from trading.auto_trading_system import AutoTradingSystem, TradingPhase
        from handlers.trading_handlers import TradingHandlers

        print("تم استيراد جميع وحدات التداول بنجاح")
    except Exception as e:
        print(f"خطأ في استيراد الوحدات: {e}")
        return False
    
    # اختبار 2: إنشاء كائنات الوحدات (بدون قاعدة بيانات)
    print("\nاختبار إنشاء كائنات النظام...")
    try:
        # إنشاء مدير المخاطر
        risk_manager = RiskManager()
        print("تم إنشاء مدير المخاطر")

        # اختبار فلترة الامتثال الشرعي
        test_symbols = ["BTC", "ETH", "BNB", "DOGE", "SHIB"]
        compliant_symbols = []

        for symbol in test_symbols:
            compliance = risk_manager.check_symbol_compliance(symbol)
            if compliance.status == "COMPLIANT":
                compliant_symbols.append(symbol)

        print(f"العملات المتوافقة شرعياً: {compliant_symbols}")

    except Exception as e:
        print(f"خطأ في إنشاء كائنات النظام: {e}")
        return False
    
    # اختبار 3: اختبار منطق التحليل
    print("\nاختبار منطق التحليل...")
    try:
        # بيانات وهمية للاختبار
        test_data = {
            'prices': [50000, 51000, 50500, 52000, 51500],
            'volumes': [1000, 1200, 900, 1500, 1100],
            'timestamps': [datetime.now() for _ in range(5)]
        }
        
        # حساب RSI وهمي
        def calculate_simple_rsi(prices, period=4):
            if len(prices) < period + 1:
                return 50  # قيمة افتراضية
            
            gains = []
            losses = []
            
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period
            
            if avg_loss == 0:
                return 100
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        rsi = calculate_simple_rsi(test_data['prices'])
        print(f"RSI المحسوب: {rsi:.2f}")

        # تحديد إشارة التداول
        if rsi < 30:
            signal = "شراء قوي"
        elif rsi < 50:
            signal = "شراء"
        elif rsi > 70:
            signal = "بيع قوي"
        elif rsi > 50:
            signal = "بيع"
        else:
            signal = "انتظار"

        print(f"إشارة التداول: {signal}")

    except Exception as e:
        print(f"خطأ في منطق التحليل: {e}")
        return False
    
    # اختبار 4: اختبار مراحل التداول
    print("\nاختبار مراحل التداول...")
    try:
        phases = [
            TradingPhase.PHASE_1_RECOMMENDATIONS,
            TradingPhase.PHASE_2_LIMITED_AUTO,
            TradingPhase.PHASE_3_FULL_AUTO
        ]

        for phase in phases:
            print(f"المرحلة متاحة: {phase.value}")

    except Exception as e:
        print(f"خطأ في مراحل التداول: {e}")
        return False

    print("\n" + "=" * 50)
    print("تم اجتياز جميع الاختبارات بنجاح!")
    print("نظام التداول الآلي جاهز للنشر")
    return True

def main():
    """الدالة الرئيسية"""
    try:
        # تشغيل الاختبارات
        result = asyncio.run(test_trading_system())
        
        if result:
            print("\nالنظام جاهز للنشر على الاستضافة!")
            sys.exit(0)
        else:
            print("\nيجب إصلاح الأخطاء قبل النشر")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nتم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
