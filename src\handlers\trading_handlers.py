"""
🤖 معالجات التليجرام للتداول الآلي
==================================

معالجات الأوامر والرسائل المتعلقة بنظام التداول الآلي
مع واجهة مستخدم سهلة ومتقدمة.

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, ConversationHandler

from trading.trading_db import TradingDatabaseManager, UserTradingSettings
from trading.auto_trading_system import AutoTradingSystem, TradingPhase
from trading.recommendation_engine import RecommendationRequest
from services.subscription_system import SubscriptionSystem

logger = logging.getLogger(__name__)

# حالات المحادثة
TRADING_SETUP, RISK_SELECTION, SYMBOL_SELECTION, AMOUNT_SELECTION = range(4)

class TradingHandlers:
    """معالجات التداول الآلي"""
    
    def __init__(self, db_manager: TradingDatabaseManager, 
                 auto_trading_system: AutoTradingSystem,
                 subscription_service: SubscriptionSystem):
        """
        تهيئة معالجات التداول
        
        Args:
            db_manager: مدير قاعدة البيانات
            auto_trading_system: نظام التداول الآلي
            subscription_service: خدمة الاشتراكات
        """
        self.db_manager = db_manager
        self.auto_trading_system = auto_trading_system
        self.subscription_service = subscription_service
    
    async def trading_menu_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة التداول الآلي"""
        try:
            user_id = str(update.effective_user.id)
            
            # فحص الاشتراك
            if not await self._check_premium_subscription(user_id):
                await update.message.reply_text(
                    "🔒 *التداول الآلي متاح للمشتركين المميزين فقط*\n\n"
                    "للاشتراك في الخدمة المميزة، استخدم الأمر /subscribe",
                    parse_mode='Markdown'
                )
                return
            
            # إنشاء لوحة المفاتيح
            keyboard = [
                [InlineKeyboardButton("🎯 التوصيات الحالية", callback_data="trading_recommendations")],
                [InlineKeyboardButton("⚙️ إعدادات التداول", callback_data="trading_settings")],
                [InlineKeyboardButton("📊 إحصائيات التداول", callback_data="trading_stats")],
                [InlineKeyboardButton("🤖 حالة النظام", callback_data="system_status")],
                [InlineKeyboardButton("📈 تحليل السوق", callback_data="market_analysis")],
                [InlineKeyboardButton("❌ إغلاق", callback_data="close_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            message = (
                "🤖 *نظام التداول الآلي*\n\n"
                "مرحباً بك في نظام التداول الآلي المتطور!\n"
                "اختر من القائمة أدناه:\n\n"
                "🎯 *التوصيات الحالية* - عرض آخر التوصيات\n"
                "⚙️ *إعدادات التداول* - تخصيص إعداداتك\n"
                "📊 *إحصائيات التداول* - مراجعة الأداء\n"
                "🤖 *حالة النظام* - معلومات النظام\n"
                "📈 *تحليل السوق* - نظرة عامة على السوق"
            )
            
            await update.message.reply_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض قائمة التداول: {str(e)}")
            await update.message.reply_text("❌ حدث خطأ في عرض قائمة التداول")
    
    async def handle_trading_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة استدعاءات أزرار التداول"""
        try:
            query = update.callback_query
            await query.answer()
            
            user_id = str(query.from_user.id)
            data = query.data
            
            if data == "trading_recommendations":
                await self._show_current_recommendations(query, user_id)
            elif data == "trading_settings":
                await self._show_trading_settings(query, user_id)
            elif data == "trading_stats":
                await self._show_trading_stats(query, user_id)
            elif data == "system_status":
                await self._show_system_status(query)
            elif data == "market_analysis":
                await self._show_market_analysis(query)
            elif data == "close_menu":
                await query.delete_message()
            elif data.startswith("setup_trading"):
                await self._start_trading_setup(query, user_id)
            elif data.startswith("toggle_trading"):
                await self._toggle_trading(query, user_id)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة استدعاء التداول: {str(e)}")
            await query.edit_message_text("❌ حدث خطأ في معالجة الطلب")
    
    async def _show_current_recommendations(self, query, user_id: str):
        """عرض التوصيات الحالية"""
        try:
            # الحصول على التوصيات النشطة
            recommendations = await self.db_manager.get_user_recommendations(
                user_id, status='active', limit=5
            )
            
            if not recommendations:
                message = (
                    "📭 *لا توجد توصيات نشطة حالياً*\n\n"
                    "سيتم إرسال توصيات جديدة عند توفرها.\n"
                    "تأكد من تفعيل إعدادات التداول الخاصة بك."
                )
            else:
                message = "🎯 *التوصيات النشطة*\n\n"
                
                for i, rec in enumerate(recommendations, 1):
                    action_emoji = "🟢" if rec.action == "buy" else "🔴"
                    risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}.get(rec.risk_level, "⚪")
                    
                    message += (
                        f"{action_emoji} *{i}. {rec.symbol}*\n"
                        f"📊 العملية: {rec.action.upper()}\n"
                        f"💰 السعر: ${rec.entry_price:.4f}\n"
                        f"🎯 الهدف: ${rec.target_price:.4f}\n"
                        f"🛡️ وقف الخسارة: ${rec.stop_loss:.4f}\n"
                        f"⭐ الثقة: {rec.confidence:.1%}\n"
                        f"{risk_emoji} المخاطرة: {rec.risk_level}\n"
                        f"⏰ انتهاء الصلاحية: {rec.expires_at.strftime('%Y-%m-%d %H:%M')}\n\n"
                    )
            
            # إضافة أزرار التحكم
            keyboard = [
                [InlineKeyboardButton("🔄 تحديث", callback_data="trading_recommendations")],
                [InlineKeyboardButton("⚙️ الإعدادات", callback_data="trading_settings")],
                [InlineKeyboardButton("🔙 العودة", callback_data="trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض التوصيات: {str(e)}")
            await query.edit_message_text("❌ حدث خطأ في عرض التوصيات")
    
    async def _show_trading_settings(self, query, user_id: str):
        """عرض إعدادات التداول"""
        try:
            # الحصول على إعدادات المستخدم
            settings = await self.db_manager.get_user_settings(user_id)
            
            if not settings:
                # إنشاء إعدادات افتراضية
                settings = UserTradingSettings(
                    user_id=user_id,
                    enabled=False,
                    risk_tolerance='moderate',
                    max_position_size=1000.0,
                    preferred_symbols=['BTC', 'ETH', 'BNB'],
                    excluded_symbols=[],
                    notification_enabled=True,
                    auto_execute=False,
                    islamic_compliance=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                await self.db_manager.save_user_settings(settings)
            
            # تنسيق الرسالة
            status_emoji = "🟢" if settings.enabled else "🔴"
            risk_emoji = {"conservative": "🟢", "moderate": "🟡", "aggressive": "🔴"}.get(settings.risk_tolerance, "⚪")
            compliance_emoji = "✅" if settings.islamic_compliance else "❌"
            
            message = (
                f"⚙️ *إعدادات التداول*\n\n"
                f"{status_emoji} الحالة: {'مفعل' if settings.enabled else 'معطل'}\n"
                f"{risk_emoji} مستوى المخاطرة: {settings.risk_tolerance}\n"
                f"💰 الحد الأقصى للمركز: ${settings.max_position_size:,.0f}\n"
                f"📱 الإشعارات: {'مفعلة' if settings.notification_enabled else 'معطلة'}\n"
                f"{compliance_emoji} الامتثال الشرعي: {'مفعل' if settings.islamic_compliance else 'معطل'}\n\n"
                f"🎯 العملات المفضلة:\n{', '.join(settings.preferred_symbols)}\n\n"
                f"⚠️ العملات المستبعدة:\n{', '.join(settings.excluded_symbols) if settings.excluded_symbols else 'لا توجد'}"
            )
            
            # إنشاء أزرار التحكم
            keyboard = [
                [InlineKeyboardButton(
                    f"{'🔴 إيقاف' if settings.enabled else '🟢 تفعيل'} التداول",
                    callback_data=f"toggle_trading_{user_id}"
                )],
                [InlineKeyboardButton("🎯 تعديل المخاطرة", callback_data="edit_risk_tolerance")],
                [InlineKeyboardButton("💰 تعديل المبلغ", callback_data="edit_position_size")],
                [InlineKeyboardButton("📱 إعدادات الإشعارات", callback_data="edit_notifications")],
                [InlineKeyboardButton("🕌 الامتثال الشرعي", callback_data="toggle_islamic_compliance")],
                [InlineKeyboardButton("🔙 العودة", callback_data="trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض إعدادات التداول: {str(e)}")
            await query.edit_message_text("❌ حدث خطأ في عرض الإعدادات")
    
    async def _show_trading_stats(self, query, user_id: str):
        """عرض إحصائيات التداول"""
        try:
            # الحصول على إحصائيات المستخدم
            recommendations = await self.db_manager.get_user_recommendations(user_id, limit=50)
            
            if not recommendations:
                message = (
                    "📊 *إحصائيات التداول*\n\n"
                    "لا توجد بيانات تداول متاحة بعد.\n"
                    "ابدأ بتفعيل التداول الآلي للحصول على الإحصائيات."
                )
            else:
                # حساب الإحصائيات
                total_recommendations = len(recommendations)
                active_count = len([r for r in recommendations if r.status == 'active'])
                executed_count = len([r for r in recommendations if r.status == 'executed'])
                expired_count = len([r for r in recommendations if r.status == 'expired'])
                
                # حساب متوسط الثقة
                avg_confidence = sum(r.confidence for r in recommendations) / total_recommendations
                
                # توزيع المخاطر
                risk_distribution = {}
                for rec in recommendations:
                    risk_distribution[rec.risk_level] = risk_distribution.get(rec.risk_level, 0) + 1
                
                message = (
                    f"📊 *إحصائيات التداول*\n\n"
                    f"📈 إجمالي التوصيات: {total_recommendations}\n"
                    f"🟢 نشطة: {active_count}\n"
                    f"✅ منفذة: {executed_count}\n"
                    f"⏰ منتهية الصلاحية: {expired_count}\n\n"
                    f"⭐ متوسط الثقة: {avg_confidence:.1%}\n\n"
                    f"📊 توزيع المخاطر:\n"
                )
                
                for risk_level, count in risk_distribution.items():
                    risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🔴"}.get(risk_level, "⚪")
                    percentage = (count / total_recommendations) * 100
                    message += f"{risk_emoji} {risk_level}: {count} ({percentage:.1f}%)\n"
            
            keyboard = [
                [InlineKeyboardButton("🔄 تحديث", callback_data="trading_stats")],
                [InlineKeyboardButton("🔙 العودة", callback_data="trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات التداول: {str(e)}")
            await query.edit_message_text("❌ حدث خطأ في عرض الإحصائيات")
    
    async def _show_system_status(self, query):
        """عرض حالة النظام"""
        try:
            # الحصول على حالة النظام
            status = self.auto_trading_system.get_system_status()
            
            status_emoji = {
                'active': '🟢',
                'paused': '🟡',
                'maintenance': '🔧',
                'emergency_stop': '🔴'
            }.get(status['status'], '⚪')
            
            phase_name = {
                'phase_1_recommendations': 'المرحلة الأولى - التوصيات',
                'phase_2_limited_auto': 'المرحلة الثانية - تداول محدود',
                'phase_3_full_auto': 'المرحلة الثالثة - تداول كامل'
            }.get(status['phase'], 'غير محدد')
            
            message = (
                f"🤖 *حالة نظام التداول الآلي*\n\n"
                f"{status_emoji} الحالة: {status['status']}\n"
                f"🔄 المرحلة: {phase_name}\n"
                f"⏰ آخر تحليل: {status['last_analysis'] or 'لم يتم بعد'}\n"
                f"📊 التوصيات النشطة: {status['active_recommendations']}\n\n"
                f"⚙️ *إعدادات النظام:*\n"
                f"🔄 فترة التحليل: {status['config']['analysis_interval']} دقيقة\n"
                f"📈 الحد الأقصى للصفقات: {status['config']['max_concurrent_trades']}\n"
                f"🛡️ حد الإيقاف الطارئ: {status['config']['emergency_stop_loss']:.1%}\n"
                f"📉 حد الخسارة اليومية: {status['config']['daily_loss_limit']:.1%}"
            )
            
            keyboard = [
                [InlineKeyboardButton("🔄 تحديث", callback_data="system_status")],
                [InlineKeyboardButton("🔙 العودة", callback_data="trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض حالة النظام: {str(e)}")
            await query.edit_message_text("❌ حدث خطأ في عرض حالة النظام")
    
    async def _show_market_analysis(self, query):
        """عرض تحليل السوق"""
        try:
            # الحصول على نظرة عامة على السوق
            market_overview = await self.auto_trading_system.market_analyzer.get_market_overview()
            
            message = (
                f"📈 *تحليل السوق العام*\n\n"
                f"📊 العملات المراقبة: {market_overview['total_symbols']}\n"
                f"🟢 إشارات صاعدة: {market_overview['bullish_signals']}\n"
                f"🔴 إشارات هابطة: {market_overview['bearish_signals']}\n"
                f"⚪ إشارات محايدة: {market_overview['neutral_signals']}\n\n"
                f"📈 عملات بحجم عالي:\n"
            )
            
            if market_overview['high_volume_symbols']:
                for symbol in market_overview['high_volume_symbols'][:5]:
                    message += f"• {symbol}\n"
            else:
                message += "لا توجد عملات بحجم تداول مرتفع حالياً\n"
            
            message += f"\n⏰ آخر تحديث: {market_overview['timestamp'].strftime('%H:%M:%S')}"
            
            keyboard = [
                [InlineKeyboardButton("🔄 تحديث", callback_data="market_analysis")],
                [InlineKeyboardButton("🔙 العودة", callback_data="trading_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                message,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض تحليل السوق: {str(e)}")
            await query.edit_message_text("❌ حدث خطأ في عرض تحليل السوق")
    
    async def _toggle_trading(self, query, user_id: str):
        """تبديل حالة التداول للمستخدم"""
        try:
            # الحصول على الإعدادات الحالية
            settings = await self.db_manager.get_user_settings(user_id)
            
            if settings:
                # تبديل الحالة
                settings.enabled = not settings.enabled
                settings.updated_at = datetime.now()
                
                # حفظ الإعدادات
                await self.db_manager.save_user_settings(settings)
                
                status = "تم تفعيل" if settings.enabled else "تم إيقاف"
                emoji = "🟢" if settings.enabled else "🔴"
                
                await query.answer(f"{emoji} {status} التداول الآلي")
                
                # إعادة عرض الإعدادات
                await self._show_trading_settings(query, user_id)
            else:
                await query.answer("❌ لم يتم العثور على إعدادات المستخدم")
                
        except Exception as e:
            logger.error(f"خطأ في تبديل حالة التداول: {str(e)}")
            await query.answer("❌ حدث خطأ في تحديث الإعدادات")
    
    async def _check_premium_subscription(self, user_id: str) -> bool:
        """فحص الاشتراك المميز"""
        try:
            subscription = await self.subscription_service.get_user_subscription(user_id)
            return subscription and subscription.is_active and subscription.plan_type == 'premium'
        except Exception as e:
            logger.error(f"خطأ في فحص الاشتراك: {str(e)}")
            return False
